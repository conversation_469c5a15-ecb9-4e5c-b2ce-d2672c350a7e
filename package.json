{"name": "vbc-web", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "server:deprecated": "node start-json-server.js", "server": "node server.js", "start": "npm run server", "dev:deprecated": "concurrently \"npm run server:json\" \"cd apps/website && npm run dev\"", "dev": "concurrently \"npm run server\" \"cd apps/website && npm run dev\"", "migrate": "node scripts/migrate-data.js", "start-app": "start-app.bat", "seed": "node seed/index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"compress.js": "^2.1.2", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "fs-extra": "^11.3.0", "json-server": "^0.17.4", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.15.0", "mongoose": "^8.12.2", "multer": "^1.4.5-lts.2", "node-fetch": "^2.7.0", "nodemailer": "^6.10.0", "rimraf": "^6.0.1"}, "devDependencies": {"eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "prettier": "^3.5.3"}}