:root {
  --primary: #000000;
  --secondary: #DC2626;
  --accent: #FFD100;
}

#root {
  max-width: none;
  margin: 0;
  padding: 0;
}

/* Improved typography */
h1 {
  font-size: 3.5rem;
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 1.25rem;
}

/* Sermon card hover effect */
.sermon-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.sermon-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.my-masonry-grid {
  display: flex;
  margin-left: -16px; /* Adjust based on your spacing */
  width: auto;
}

.my-masonry-grid_column {
  padding-left: 16px; /* Adjust for spacing */
  background-clip: padding-box;
}

.my-masonry-grid img {
  width: 100%;
  display: block;
}

/* Smooth transitions for all hover states */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* Backdrop blur effect */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}