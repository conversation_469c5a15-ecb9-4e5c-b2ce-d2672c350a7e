<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>API Test</h1>
    
    <div>
        <button id="testEvents">Test Events API</button>
        <button id="testSermons">Test Sermons API</button>
        <button id="testLeaders">Test Leaders API</button>
        <button id="testCellGroups">Test Cell Groups API</button>
    </div>
    
    <h2>Results:</h2>
    <pre id="results">Click a button to test an API endpoint...</pre>
    
    <script>
        const API_URL = 'http://localhost:3000';
        
        document.getElementById('testEvents').addEventListener('click', async () => {
            await testApi('api/events', 'Events');
        });
        
        document.getElementById('testSermons').addEventListener('click', async () => {
            await testApi('api/sermons', 'Sermons');
        });
        
        document.getElementById('testLeaders').addEventListener('click', async () => {
            await testApi('api/leaders', 'Leaders');
        });
        
        document.getElementById('testCellGroups').addEventListener('click', async () => {
            await testApi('api/cell-groups', 'Cell Groups');
        });
        
        async function testApi(endpoint, name) {
            const resultsElement = document.getElementById('results');
            resultsElement.textContent = `Testing ${name} API...`;
            
            try {
                const startTime = new Date();
                const response = await fetch(`${API_URL}/${endpoint}`);
                const endTime = new Date();
                const timeElapsed = endTime - startTime;
                
                if (!response.ok) {
                    throw new Error(`API returned status ${response.status}`);
                }
                
                const data = await response.json();
                
                resultsElement.textContent = `${name} API Test Results (${timeElapsed}ms):\n\n`;
                resultsElement.textContent += `Total items: ${Array.isArray(data) ? data.length : 'N/A'}\n\n`;
                resultsElement.textContent += JSON.stringify(data, null, 2);
            } catch (error) {
                resultsElement.innerHTML = `<span class="error">Error testing ${name} API: ${error.message}</span>`;
                console.error(`Error testing ${name} API:`, error);
            }
        }
    </script>
</body>
</html>
