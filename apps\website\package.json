{"name": "website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "tw:init": "tailwindcss init -p"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@react-google-maps/api": "^2.20.6", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.74.4", "framer-motion": "^12.0.11", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "prop-types": "^15.8.1", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "18.2.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-icons": "^5.4.0", "react-lazy-load-image-component": "^1.6.3", "react-masonry-css": "^1.0.16", "react-modal": "^3.16.3", "react-router-dom": "6.14.2", "react-toastify": "^11.0.5", "react-vertical-timeline-component": "^3.5.3"}, "devDependencies": {"@heroicons/react": "^2.1.1", "@types/react": "18.2.55", "@types/react-dom": "18.2.19", "@types/react-modal": "^3.16.3", "@vitejs/plugin-react": "4.2.1", "autoprefixer": "10.4.18", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-webpack-plugin": "^2.4.2", "postcss": "8.4.35", "postcss-cli": "10.1.0", "tailwindcss": "3.4.3", "vite": "5.2.8"}}