const mongoose = require("mongoose");
const seedFoundationClassSessions = require("./foundationClassSessions");
const seedRecurringEvents = require("./recurringEvents");

// MongoDB connection string from environment or use default
const MONGODB_URI =
  process.env.MONGODB_URI || "mongodb://localhost:27017/vbc-web";

// Main seed function
const seedDatabase = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log("Connected to MongoDB");

    // Run seed functions
    await seedFoundationClassSessions();
    await seedRecurringEvents();

    console.log("Database seeding completed successfully");

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log("Disconnected from MongoDB");

    process.exit(0);
  } catch (error) {
    console.error("Error seeding database:", error);

    // Disconnect from MongoDB
    try {
      await mongoose.disconnect();
      console.log("Disconnected from MongoDB after error");
    } catch (disconnectError) {
      console.error("Error disconnecting from MongoDB:", disconnectError);
    }

    process.exit(1);
  }
};

// Run the seed function if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = seedDatabase;
